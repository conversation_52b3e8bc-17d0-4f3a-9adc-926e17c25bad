<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirPods Pro - Premium Wireless Earbuds</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .hero-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 100px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 50px;
            opacity: 0.9;
        }

        .headphones-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 60px 0;
        }

        .headphones-container {
            position: relative;
            width: 400px;
            height: 400px;
        }

        .charging-case {
            width: 200px;
            height: 120px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 50px;
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .charging-case::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background: linear-gradient(145deg, #fff, #f1f3f4);
            border-radius: 40px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        .airpod {
            width: 40px;
            height: 120px;
            background: linear-gradient(145deg, #fff, #f8f9fa);
            border-radius: 20px 20px 50px 50px;
            position: absolute;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .airpod-left {
            left: 80px;
            top: 50px;
            transform: rotate(-15deg);
            animation: float 3s ease-in-out infinite;
        }

        .airpod-right {
            right: 80px;
            top: 50px;
            transform: rotate(15deg);
            animation: float 3s ease-in-out infinite 1.5s;
        }

        .airpod::before {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 15px;
            height: 15px;
            background: #333;
            border-radius: 50%;
        }

        .airpod::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 25px;
            height: 25px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 50%;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(-15deg); }
            50% { transform: translateY(-20px) rotate(-15deg); }
        }

        .airpod-right {
            animation-name: float-right;
        }

        @keyframes float-right {
            0%, 100% { transform: translateY(0) rotate(15deg); }
            50% { transform: translateY(-20px) rotate(15deg); }
        }

        .product-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .product-info h2 {
            font-size: 3rem;
            margin-bottom: 30px;
            color: #333;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff6b6b;
            margin-bottom: 40px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: white;
        }

        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
        }

        .buy-section {
            text-align: center;
        }

        .buy-button {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            margin-bottom: 20px;
        }

        .buy-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .shipping-info {
            color: #666;
            font-size: 0.9rem;
        }

        .specs-section {
            padding: 80px 0;
            background: white;
        }

        .specs-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 60px;
            color: #333;
        }

        .specs-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .spec-category {
            background: #f8f9fa;
            padding: 40px;
            border-radius: 20px;
            border-top: 5px solid #ff6b6b;
        }

        .spec-category h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #333;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 600;
            color: #495057;
        }

        .spec-value {
            color: #6c757d;
            text-align: right;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .headphones-container {
                width: 300px;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="hero-title">AirPods Pro</h1>
            <p class="hero-subtitle">Adaptive Audio. Now playing.</p>
            
            <div class="headphones-showcase">
                <div class="headphones-container">
                    <div class="airpod airpod-left"></div>
                    <div class="airpod airpod-right"></div>
                    <div class="charging-case"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="product-section">
        <div class="container">
            <div class="product-grid">
                <div class="product-info">
                    <h2>Magical. Effortless.</h2>
                    <div class="price">$249</div>
                    
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">🔇</div>
                            <div class="feature-title">Active Noise Cancellation</div>
                            <div class="feature-desc">Block out the world</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">🎵</div>
                            <div class="feature-title">Adaptive Audio</div>
                            <div class="feature-desc">Automatically adjusts to your environment</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">🔋</div>
                            <div class="feature-title">30-Hour Battery</div>
                            <div class="feature-desc">With charging case</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">💧</div>
                            <div class="feature-title">Sweat Resistant</div>
                            <div class="feature-desc">IPX4 water resistance</div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="buy-button">Buy Now</button>
                        <div class="shipping-info">Free shipping and returns</div>
                    </div>
                </div>
                
                <div class="headphones-showcase">
                    <div class="headphones-container">
                        <div class="airpod airpod-left"></div>
                        <div class="airpod airpod-right"></div>
                        <div class="charging-case"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="specs-section">
        <div class="container">
            <h2 class="specs-title">Technical Specifications</h2>
            <div class="specs-container">
                <div class="spec-category">
                    <h3>Audio</h3>
                    <div class="spec-item">
                        <span class="spec-label">Driver</span>
                        <span class="spec-value">Custom high-excursion</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Frequency Response</span>
                        <span class="spec-value">20Hz to 20,000Hz</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Noise Cancellation</span>
                        <span class="spec-value">Active & Adaptive</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Spatial Audio</span>
                        <span class="spec-value">Yes, with head tracking</span>
                    </div>
                </div>
                
                <div class="spec-category">
                    <h3>Battery & Charging</h3>
                    <div class="spec-item">
                        <span class="spec-label">Listening Time</span>
                        <span class="spec-value">Up to 6 hours</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">With Case</span>
                        <span class="spec-value">Up to 30 hours</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Quick Charge</span>
                        <span class="spec-value">5 min = 1 hour</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Charging</span>
                        <span class="spec-value">Lightning & Wireless</span>
                    </div>
                </div>
                
                <div class="spec-category">
                    <h3>Connectivity & Controls</h3>
                    <div class="spec-item">
                        <span class="spec-label">Chip</span>
                        <span class="spec-value">Apple H2</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Bluetooth</span>
                        <span class="spec-value">5.3</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Controls</span>
                        <span class="spec-value">Touch & Voice</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Siri</span>
                        <span class="spec-value">Hey Siri ready</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.buy-button').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-3px)';
                alert('Thank you for your interest! This is a demo page.');
            }, 150);
        });

        // Add floating animation to AirPods
        const airpods = document.querySelectorAll('.airpod');
        airpods.forEach((airpod, index) => {
            airpod.addEventListener('mouseenter', function() {
                this.style.animationPlayState = 'paused';
                this.style.transform += ' scale(1.1)';
            });
            
            airpod.addEventListener('mouseleave', function() {
                this.style.animationPlayState = 'running';
                this.style.transform = this.style.transform.replace(' scale(1.1)', '');
            });
        });
    </script>
</body>
</html>