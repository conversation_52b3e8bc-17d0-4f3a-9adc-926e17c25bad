Word图片链接提取器 - 使用说明
================================

程序已经编译完成，可以直接使用！

运行方法：
---------

方法1：使用批处理文件（推荐）
run-simple.bat 你的文档.docx

方法2：直接使用Java命令
java -cp "target/classes;target/dependency/*" com.example.WordImageLinkExtractor 你的文档.docx

准备测试文档：
-----------
1. 打开Microsoft Word
2. 插入一张图片（插入 → 图片）
3. 右键点击图片，选择"超链接"
4. 输入网址（如：https://www.baidu.com）
5. 保存为.docx格式

示例输出：
---------
Found 1 image link(s):
================================================================================
Image Link #1:
  Image ID: rId5
  Image Name: image1.png
  Link URL: https://www.baidu.com

程序功能：
---------
- 提取Word文档中图片的超链接地址
- 显示图片ID、文件名和链接URL
- 支持段落、表格、页眉页脚中的图片链接

注意事项：
---------
- 只支持.docx格式（不支持.doc）
- 图片必须设置了超链接才能被提取
- 确保文档路径正确