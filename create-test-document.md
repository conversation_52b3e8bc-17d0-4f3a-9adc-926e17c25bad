# How to Create a Test Word Document with Image Links

To test the WordImageLinkExtractor program, you need to create a Word document (.docx) with images that have hyperlinks. Here's how:

## Steps to Create Test Document:

1. **Open Microsoft Word** and create a new document

2. **Insert an Image:**
   - Go to Insert → Pictures
   - Choose an image from your computer
   - Insert it into the document

3. **Add Hyperlink to the Image:**
   - Right-click on the inserted image
   - Select "Hyperlink" or "Link" from the context menu
   - In the dialog box, enter a URL (e.g., https://www.google.com)
   - Click OK

4. **Repeat for Multiple Images** (optional):
   - Insert more images and add different hyperlinks to test multiple scenarios

5. **Save the Document:**
   - Save as .docx format (e.g., "test-document.docx")

## Example Test URLs:
- https://www.google.com
- https://www.microsoft.com
- https://www.github.com
- https://www.stackoverflow.com

## Running the Program:

Once you have created the test document, run:

```bash
# Using Maven
mvn exec:java -Dexec.mainClass="com.example.WordImageLinkExtractor" -Dexec.args="test-document.docx"

# Or using the batch file (Windows)
run.bat test-document.docx
```

The program will output information about each image link found in the document.