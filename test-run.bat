@echo off
echo Word Image Link Extractor - Test Runner
echo =====================================
echo.

if "%1"=="" (
    echo Please provide a Word document path as argument.
    echo Usage: test-run.bat [path-to-docx-file]
    echo Example: test-run.bat test-document.docx
    echo.
    pause
    exit /b 1
)

if not exist "%1" (
    echo Error: File "%1" does not exist.
    echo Please check the file path and try again.
    echo.
    pause
    exit /b 1
)

echo Testing with document: %1
echo.
echo Running Word Image Link Extractor...
echo ====================================
echo.

call mvn exec:java -Dexec.mainClass="com.example.WordImageLinkExtractor" -Dexec.args="%1"

echo.
echo ====================================
echo Test completed.
pause