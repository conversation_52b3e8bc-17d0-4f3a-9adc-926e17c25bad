<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1920" height="1080" fill="#f8f9fa"/>

  <!-- Header -->
  <rect x="0" y="0" width="1920" height="150" fill="#CC0000"/>
  <text x="960" y="100" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="64" font-weight="bold">
    China's Retirement Policy Changes
  </text>

  <!-- Original Text - Large and Clear for Reading -->
  <g transform="translate(100, 200)">
    <rect x="0" y="0" width="1720" height="500" fill="white" stroke="#CC0000" stroke-width="3" rx="15"/>
    <text x="860" y="60" text-anchor="middle" fill="#CC0000" font-family="Arial, sans-serif" font-size="48" font-weight="bold">
      📖 Original Policy Text (Read Aloud)
    </text>

    <!-- First paragraph -->
    <text x="100" y="130" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      In September 2024, China introduced a gradual delayed retirement policy.
    </text>
    <text x="100" y="180" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      According to the policy, starting from 2025, it will take 15 years to raise
    </text>
    <text x="100" y="230" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      the retirement age step by step. The retirement age for male workers
    </text>
    <text x="100" y="280" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      will go up from 60 to 63 years old.
    </text>

    <!-- Second paragraph -->
    <text x="100" y="350" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      For female workers, those who should retire at 50 will have their
    </text>
    <text x="100" y="400" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      retirement age increased to 55. Those who should retire at 55 will
    </text>
    <text x="100" y="450" fill="#333" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
      have their retirement age increased to 58.
    </text>
  </g>

  <!-- Simple Visual Summary -->
  <g transform="translate(100, 750)">
    <rect x="0" y="0" width="1720" height="250" fill="#e8f5e8" stroke="#4caf50" stroke-width="3" rx="15"/>
    <text x="860" y="60" text-anchor="middle" fill="#4caf50" font-family="Arial, sans-serif" font-size="48" font-weight="bold">
      📊 Simple Summary
    </text>

    <!-- Three key points with big icons -->
    <g transform="translate(200, 100)">
      <!-- Men -->
      <circle cx="200" cy="80" r="60" fill="#2196f3"/>
      <text x="200" y="70" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="40">👨</text>
      <text x="200" y="100" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">60→63</text>
      <text x="200" y="160" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Men work 3 more years</text>

      <!-- Women -->
      <circle cx="660" cy="80" r="60" fill="#e91e63"/>
      <text x="660" y="70" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="40">👩</text>
      <text x="660" y="100" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">50→55/55→58</text>
      <text x="660" y="160" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Women work 3-5 more years</text>

      <!-- Timeline -->
      <circle cx="1120" cy="80" r="60" fill="#ff9800"/>
      <text x="1120" y="70" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="40">📅</text>
      <text x="1120" y="100" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">2025-2040</text>
      <text x="1120" y="160" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="24" font-weight="bold">15 years to change</text>
    </g>
  </g>

  
  <!-- Page Number -->
  <text x="1820" y="1040" text-anchor="end" fill="#666" font-family="Arial, sans-serif" font-size="24">
    Page 1 of 3
  </text>
</svg>
