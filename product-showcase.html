<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Product Showcase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #666;
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f8f9fa"/><circle cx="50" cy="40" r="15" fill="%23dee2e6"/><path d="M20 70 L50 50 L80 70 L80 85 L20 85 Z" fill="%23adb5bd"/></svg>') center/cover;
            opacity: 0.3;
        }

        .product-content {
            padding: 30px;
        }

        .product-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .product-price {
            font-size: 2rem;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 20px;
        }

        .features {
            margin-bottom: 25px;
        }

        .features h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }

        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .buy-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .buy-button:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .specs-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-top: 50px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .specs-title {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .spec-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .spec-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 5px;
        }

        .spec-value {
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>New Product Showcase</h1>
            <p>Discover our latest innovative products</p>
        </div>

        <div class="product-grid">
            <!-- Product 1 -->
            <div class="product-card">
                <div class="product-image">
                    Product Image Placeholder
                </div>
                <div class="product-content">
                    <h2 class="product-title">Smart Wireless Headphones</h2>
                    <div class="product-price">$199.99</div>
                    <div class="features">
                        <h3>Key Features</h3>
                        <ul>
                            <li>Active Noise Cancellation</li>
                            <li>30-hour Battery Life</li>
                            <li>Bluetooth 5.0 Connectivity</li>
                            <li>Premium Sound Quality</li>
                            <li>Comfortable Over-ear Design</li>
                        </ul>
                    </div>
                    <a href="#" class="buy-button">Buy Now</a>
                </div>
            </div>

            <!-- Product 2 -->
            <div class="product-card">
                <div class="product-image">
                    Product Image Placeholder
                </div>
                <div class="product-content">
                    <h2 class="product-title">Fitness Smartwatch</h2>
                    <div class="product-price">$299.99</div>
                    <div class="features">
                        <h3>Key Features</h3>
                        <ul>
                            <li>Heart Rate Monitoring</li>
                            <li>GPS Tracking</li>
                            <li>Water Resistant (50m)</li>
                            <li>7-day Battery Life</li>
                            <li>Multiple Sport Modes</li>
                        </ul>
                    </div>
                    <a href="#" class="buy-button">Buy Now</a>
                </div>
            </div>

            <!-- Product 3 -->
            <div class="product-card">
                <div class="product-image">
                    Product Image Placeholder
                </div>
                <div class="product-content">
                    <h2 class="product-title">Portable Power Bank</h2>
                    <div class="product-price">$79.99</div>
                    <div class="features">
                        <h3>Key Features</h3>
                        <ul>
                            <li>20,000mAh Capacity</li>
                            <li>Fast Charging Technology</li>
                            <li>Multiple USB Ports</li>
                            <li>LED Power Indicator</li>
                            <li>Compact & Lightweight</li>
                        </ul>
                    </div>
                    <a href="#" class="buy-button">Buy Now</a>
                </div>
            </div>
        </div>

        <div class="specs-section">
            <h2 class="specs-title">Technical Specifications</h2>
            <div class="specs-grid">
                <div class="spec-item">
                    <div class="spec-label">Warranty</div>
                    <div class="spec-value">2 Years International Warranty</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Shipping</div>
                    <div class="spec-value">Free Worldwide Shipping</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Return Policy</div>
                    <div class="spec-value">30-Day Money Back Guarantee</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Support</div>
                    <div class="spec-value">24/7 Customer Support</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add smooth scrolling and interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add click animation to buy buttons
            const buyButtons = document.querySelectorAll('.buy-button');
            buyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255,255,255,0.5);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                    
                    // Simulate purchase action
                    setTimeout(() => {
                        alert('Thank you for your interest! This is a demo page.');
                    }, 300);
                });
            });
            
            // Add CSS for ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>