<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MacBook Pro 16" - Professional Laptop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1d1d1f;
            background: #f5f5f7;
        }

        .hero-section {
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #fff, #ccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 40px;
            opacity: 0.8;
        }

        .laptop-showcase {
            position: relative;
            margin: 60px 0;
        }

        .laptop-mockup {
            width: 600px;
            height: 400px;
            margin: 0 auto;
            position: relative;
            transform: perspective(1000px) rotateX(15deg);
            transition: transform 0.3s ease;
        }

        .laptop-mockup:hover {
            transform: perspective(1000px) rotateX(10deg) scale(1.05);
        }

        .laptop-base {
            width: 100%;
            height: 20px;
            background: linear-gradient(135deg, #2c2c2c, #1a1a1a);
            border-radius: 0 0 20px 20px;
            position: absolute;
            bottom: 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }

        .laptop-screen {
            width: 100%;
            height: 380px;
            background: linear-gradient(135deg, #2c2c2c, #1a1a1a);
            border-radius: 10px 10px 0 0;
            position: relative;
            padding: 15px;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.3);
        }

        .screen-content {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #007aff, #5856d6);
            border-radius: 5px;
            position: relative;
            overflow: hidden;
        }

        .screen-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 570 350"><rect width="570" height="350" fill="%23000"/><rect x="0" y="0" width="570" height="30" fill="%23333"/><circle cx="15" cy="15" r="6" fill="%23ff5f57"/><circle cx="35" cy="15" r="6" fill="%23ffbd2e"/><circle cx="55" cy="15" r="6" fill="%2328ca42"/><rect x="20" y="50" width="530" height="280" rx="10" fill="%23f8f9fa"/><rect x="40" y="70" width="200" height="30" rx="15" fill="%23007aff"/><rect x="40" y="120" width="490" height="20" rx="10" fill="%23e5e5e7"/><rect x="40" y="160" width="300" height="15" rx="7" fill="%23d1d1d6"/><rect x="40" y="190" width="400" height="15" rx="7" fill="%23d1d1d6"/><rect x="40" y="220" width="250" height="15" rx="7" fill="%23d1d1d6"/><rect x="350" y="120" width="180" height="120" rx="10" fill="%23007aff" opacity="0.1"/><circle cx="440" cy="180" r="30" fill="%23007aff"/></svg>') center/cover;
        }

        .keyboard-area {
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            height: 8px;
            background: linear-gradient(135deg, #333, #1a1a1a);
            border-radius: 4px;
        }

        .product-details {
            background: white;
            padding: 80px 0;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .product-info h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #1d1d1f;
        }

        .price {
            font-size: 2rem;
            font-weight: 700;
            color: #007aff;
            margin-bottom: 30px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
        }

        .features-list li {
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #007aff, #5856d6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 1.2rem;
        }

        .buy-button {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
        }

        .buy-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 122, 255, 0.4);
        }

        .specs-section {
            background: #f5f5f7;
            padding: 80px 0;
        }

        .specs-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 60px;
            color: #1d1d1f;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .spec-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .spec-card h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #007aff;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .laptop-mockup {
                width: 400px;
                height: 267px;
            }
            
            .details-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .specs-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="hero-title">MacBook Pro 16"</h1>
            <p class="hero-subtitle">Supercharged for pros.</p>
            
            <div class="laptop-showcase">
                <div class="laptop-mockup">
                    <div class="laptop-screen">
                        <div class="screen-content"></div>
                    </div>
                    <div class="laptop-base">
                        <div class="keyboard-area"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="product-details">
        <div class="container">
            <div class="details-grid">
                <div class="product-info">
                    <h2>Power. Performance. Pro.</h2>
                    <div class="price">From $2,499</div>
                    
                    <ul class="features-list">
                        <li>
                            <div class="feature-icon">🚀</div>
                            <div>
                                <strong>M3 Max Chip</strong><br>
                                <small>Up to 40-core GPU for extreme performance</small>
                            </div>
                        </li>
                        <li>
                            <div class="feature-icon">💾</div>
                            <div>
                                <strong>Up to 128GB RAM</strong><br>
                                <small>Unified memory for seamless multitasking</small>
                            </div>
                        </li>
                        <li>
                            <div class="feature-icon">📺</div>
                            <div>
                                <strong>16.2" Liquid Retina XDR</strong><br>
                                <small>Extreme Dynamic Range display</small>
                            </div>
                        </li>
                        <li>
                            <div class="feature-icon">🔋</div>
                            <div>
                                <strong>22-hour battery life</strong><br>
                                <small>All-day power for professional workflows</small>
                            </div>
                        </li>
                    </ul>
                    
                    <button class="buy-button">Buy Now</button>
                </div>
                
                <div class="laptop-showcase">
                    <div class="laptop-mockup">
                        <div class="laptop-screen">
                            <div class="screen-content"></div>
                        </div>
                        <div class="laptop-base">
                            <div class="keyboard-area"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="specs-section">
        <div class="container">
            <h2 class="specs-title">Technical Specifications</h2>
            <div class="specs-grid">
                <div class="spec-card">
                    <h3>Display</h3>
                    <div class="spec-item">
                        <span>Size</span>
                        <span>16.2 inches</span>
                    </div>
                    <div class="spec-item">
                        <span>Resolution</span>
                        <span>3456 x 2234</span>
                    </div>
                    <div class="spec-item">
                        <span>Technology</span>
                        <span>Liquid Retina XDR</span>
                    </div>
                    <div class="spec-item">
                        <span>Brightness</span>
                        <span>1000 nits sustained</span>
                    </div>
                </div>
                
                <div class="spec-card">
                    <h3>Performance</h3>
                    <div class="spec-item">
                        <span>Chip</span>
                        <span>Apple M3 Max</span>
                    </div>
                    <div class="spec-item">
                        <span>CPU</span>
                        <span>16-core</span>
                    </div>
                    <div class="spec-item">
                        <span>GPU</span>
                        <span>Up to 40-core</span>
                    </div>
                    <div class="spec-item">
                        <span>Memory</span>
                        <span>Up to 128GB</span>
                    </div>
                </div>
                
                <div class="spec-card">
                    <h3>Storage & Connectivity</h3>
                    <div class="spec-item">
                        <span>Storage</span>
                        <span>Up to 8TB SSD</span>
                    </div>
                    <div class="spec-item">
                        <span>Ports</span>
                        <span>3x Thunderbolt 4</span>
                    </div>
                    <div class="spec-item">
                        <span>Wi-Fi</span>
                        <span>Wi-Fi 6E</span>
                    </div>
                    <div class="spec-item">
                        <span>Bluetooth</span>
                        <span>Bluetooth 5.3</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.buy-button').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-3px)';
                alert('Thank you for your interest! This is a demo page.');
            }, 150);
        });
    </script>
</body>
</html>