<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPhone 15 Pro - Premium Smartphone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .product-hero {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            align-items: center;
            min-height: 80vh;
            background: white;
            border-radius: 20px;
            padding: 60px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .product-image-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .smartphone-mockup {
            width: 300px;
            height: 600px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transform: perspective(1000px) rotateY(-15deg);
            transition: transform 0.3s ease;
        }

        .smartphone-mockup:hover {
            transform: perspective(1000px) rotateY(0deg);
        }

        .screen {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 30px;
            overflow: hidden;
        }

        .screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 260 560"><rect width="260" height="560" fill="%23000"/><rect x="20" y="20" width="220" height="40" rx="20" fill="%23333"/><circle cx="40" cy="40" r="8" fill="%2300ff00"/><circle cx="60" cy="40" r="8" fill="%23ffff00"/><circle cx="80" cy="40" r="8" fill="%23ff0000"/><rect x="20" y="80" width="220" height="460" rx="10" fill="%23f8f9fa"/><rect x="40" y="100" width="180" height="20" rx="10" fill="%23dee2e6"/><rect x="40" y="140" width="120" height="15" rx="7" fill="%23adb5bd"/><rect x="40" y="170" width="160" height="15" rx="7" fill="%23adb5bd"/><circle cx="130" cy="300" r="60" fill="%23007bff"/><rect x="40" y="400" width="180" height="40" rx="20" fill="%2328a745"/></svg>') center/cover;
        }

        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 25px;
            background: #1a1a1a;
            border-radius: 0 0 15px 15px;
        }

        .camera-bump {
            position: absolute;
            top: 40px;
            right: 30px;
            width: 80px;
            height: 80px;
            background: #333;
            border-radius: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5);
        }

        .camera-lens {
            position: absolute;
            width: 25px;
            height: 25px;
            background: radial-gradient(circle, #000 30%, #333 70%);
            border-radius: 50%;
            top: 15px;
            left: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .camera-lens:nth-child(2) {
            top: 15px;
            right: 15px;
            left: auto;
        }

        .camera-lens:nth-child(3) {
            bottom: 15px;
            left: 15px;
            top: auto;
        }

        .product-info {
            padding: 20px 0;
        }

        .product-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .product-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 30px;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .buy-section {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .buy-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .buy-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .specs-section {
            background: white;
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .specs-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .spec-category {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-top: 4px solid #667eea;
        }

        .spec-category h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .product-hero {
                grid-template-columns: 1fr;
                padding: 30px;
                text-align: center;
            }
            
            .product-title {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="product-hero">
            <div class="product-image-container">
                <div class="smartphone-mockup">
                    <div class="screen">
                        <div class="notch"></div>
                    </div>
                    <div class="camera-bump">
                        <div class="camera-lens"></div>
                        <div class="camera-lens"></div>
                        <div class="camera-lens"></div>
                    </div>
                </div>
            </div>
            
            <div class="product-info">
                <h1 class="product-title">iPhone 15 Pro</h1>
                <p class="product-subtitle">Titanium. So strong. So light. So Pro.</p>
                <div class="price">$999</div>
                
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">📱</div>
                        <div>
                            <strong>6.1" Super Retina XDR</strong><br>
                            <small>ProMotion Technology</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📸</div>
                        <div>
                            <strong>48MP Main Camera</strong><br>
                            <small>Pro Camera System</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <div>
                            <strong>A17 Pro Chip</strong><br>
                            <small>3nm Technology</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🔋</div>
                        <div>
                            <strong>All-Day Battery</strong><br>
                            <small>Up to 23 hours video</small>
                        </div>
                    </div>
                </div>
                
                <div class="buy-section">
                    <button class="buy-button">Buy Now</button>
                    <span style="color: #666;">Free shipping & 30-day returns</span>
                </div>
            </div>
        </div>
        
        <div class="specs-section">
            <h2 class="specs-title">Technical Specifications</h2>
            <div class="specs-grid">
                <div class="spec-category">
                    <h3>Display</h3>
                    <div class="spec-item">
                        <span>Size</span>
                        <span>6.1 inches</span>
                    </div>
                    <div class="spec-item">
                        <span>Resolution</span>
                        <span>2556 x 1179</span>
                    </div>
                    <div class="spec-item">
                        <span>Technology</span>
                        <span>Super Retina XDR OLED</span>
                    </div>
                    <div class="spec-item">
                        <span>Refresh Rate</span>
                        <span>120Hz ProMotion</span>
                    </div>
                </div>
                
                <div class="spec-category">
                    <h3>Camera</h3>
                    <div class="spec-item">
                        <span>Main Camera</span>
                        <span>48MP f/1.78</span>
                    </div>
                    <div class="spec-item">
                        <span>Ultra Wide</span>
                        <span>12MP f/2.2</span>
                    </div>
                    <div class="spec-item">
                        <span>Telephoto</span>
                        <span>12MP f/2.8</span>
                    </div>
                    <div class="spec-item">
                        <span>Video</span>
                        <span>4K ProRes</span>
                    </div>
                </div>
                
                <div class="spec-category">
                    <h3>Performance</h3>
                    <div class="spec-item">
                        <span>Chip</span>
                        <span>A17 Pro</span>
                    </div>
                    <div class="spec-item">
                        <span>Storage</span>
                        <span>128GB - 1TB</span>
                    </div>
                    <div class="spec-item">
                        <span>RAM</span>
                        <span>8GB</span>
                    </div>
                    <div class="spec-item">
                        <span>5G</span>
                        <span>Sub-6 GHz and mmWave</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.buy-button').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-3px)';
                alert('Thank you for your interest! This is a demo page.');
            }, 150);
        });
    </script>
</body>
</html>