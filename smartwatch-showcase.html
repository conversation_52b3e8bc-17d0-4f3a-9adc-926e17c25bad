<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple Watch Ultra - Adventure Ready</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1d1d1f;
            background: #000;
        }

        .hero-section {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 100px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 20px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.8rem;
            margin-bottom: 60px;
            opacity: 0.9;
        }

        .watch-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 80px 0;
        }

        .watch-container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        .watch-body {
            width: 200px;
            height: 240px;
            background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
            border-radius: 50px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            border: 3px solid #ff6b35;
        }

        .watch-screen {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            background: linear-gradient(135deg, #000, #1a1a1a);
            border-radius: 35px;
            overflow: hidden;
        }

        .watch-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 170 210"><rect width="170" height="210" fill="%23000"/><circle cx="85" cy="60" r="25" fill="%23ff6b35"/><text x="85" y="67" text-anchor="middle" fill="white" font-size="12" font-weight="bold">12:34</text><rect x="20" y="100" width="130" height="8" rx="4" fill="%23333"/><rect x="20" y="100" width="80" height="8" rx="4" fill="%2300ff00"/><text x="85" y="125" text-anchor="middle" fill="%23999" font-size="8">Heart Rate: 72 BPM</text><circle cx="40" cy="150" r="12" fill="%23007aff"/><circle cx="85" cy="150" r="12" fill="%2334c759"/><circle cx="130" cy="150" r="12" fill="%23ff3b30"/><text x="85" y="185" text-anchor="middle" fill="%23666" font-size="6">Activity Rings</text></svg>') center/cover;
        }

        .digital-crown {
            position: absolute;
            right: -8px;
            top: 40px;
            width: 16px;
            height: 30px;
            background: linear-gradient(145deg, #ff6b35, #f7931e);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .side-button {
            position: absolute;
            right: -6px;
            top: 80px;
            width: 12px;
            height: 20px;
            background: linear-gradient(145deg, #666, #333);
            border-radius: 6px;
        }

        .watch-band {
            position: absolute;
            width: 40px;
            height: 300px;
            background: linear-gradient(145deg, #ff6b35, #f7931e);
            border-radius: 20px;
            top: 0;
        }

        .watch-band.left {
            left: -20px;
            transform: rotate(-10deg);
        }

        .watch-band.right {
            right: -20px;
            transform: rotate(10deg);
        }

        .product-section {
            background: white;
            padding: 100px 0;
        }

        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .product-info h2 {
            font-size: 3.5rem;
            margin-bottom: 30px;
            color: #1d1d1f;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff6b35;
            margin-bottom: 40px;
        }

        .key-features {
            margin-bottom: 50px;
        }

        .feature-highlight {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: #f5f5f7;
            border-radius: 15px;
            border-left: 5px solid #ff6b35;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 1.2rem;
        }

        .feature-text h3 {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1d1d1f;
        }

        .feature-text p {
            color: #86868b;
            font-size: 0.9rem;
        }

        .buy-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
        }

        .buy-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 53, 0.4);
        }

        .specs-section {
            background: #f5f5f7;
            padding: 100px 0;
        }

        .specs-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 60px;
            color: #1d1d1f;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .spec-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-top: 5px solid #ff6b35;
        }

        .spec-card h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #1d1d1f;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 600;
            color: #1d1d1f;
        }

        .spec-value {
            color: #86868b;
            text-align: right;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }
            
            .watch-container {
                width: 250px;
                height: 250px;
            }
            
            .watch-body {
                width: 160px;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="hero-title">Apple Watch Ultra</h1>
            <p class="hero-subtitle">Adventure awaits.</p>
            
            <div class="watch-showcase">
                <div class="watch-container">
                    <div class="watch-band left"></div>
                    <div class="watch-band right"></div>
                    <div class="watch-body">
                        <div class="watch-screen"></div>
                        <div class="digital-crown"></div>
                        <div class="side-button"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="product-section">
        <div class="container">
            <div class="product-grid">
                <div class="product-info">
                    <h2>Built for extremes.</h2>
                    <div class="price">$799</div>
                    
                    <div class="key-features">
                        <div class="feature-highlight">
                            <div class="feature-icon">🏔️</div>
                            <div class="feature-text">
                                <h3>Rugged Titanium Case</h3>
                                <p>Lightweight, durable, and corrosion-resistant</p>
                            </div>
                        </div>
                        
                        <div class="feature-highlight">
                            <div class="feature-icon">🌊</div>
                            <div class="feature-text">
                                <h3>100m Water Resistance</h3>
                                <p>Perfect for swimming and water sports</p>
                            </div>
                        </div>
                        
                        <div class="feature-highlight">
                            <div class="feature-icon">🔋</div>
                            <div class="feature-text">
                                <h3>36-Hour Battery Life</h3>
                                <p>Up to 60 hours in Low Power Mode</p>
                            </div>
                        </div>
                        
                        <div class="feature-highlight">
                            <div class="feature-icon">📍</div>
                            <div class="feature-text">
                                <h3>Precision GPS</h3>
                                <p>Dual-frequency GPS for accurate tracking</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="buy-button">Buy Now</button>
                </div>
                
                <div class="watch-showcase">
                    <div class="watch-container">
                        <div class="watch-band left"></div>
                        <div class="watch-band right"></div>
                        <div class="watch-body">
                            <div class="watch-screen"></div>
                            <div class="digital-crown"></div>
                            <div class="side-button"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="specs-section">
        <div class="container">
            <h2 class="specs-title">Technical Specifications</h2>
            <div class="specs-grid">
                <div class="spec-card">
                    <h3>Display</h3>
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        <span class="spec-value">49mm</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        <span class="spec-value">410 x 502 pixels</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Brightness</span>
                        <span class="spec-value">2000 nits</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Always-On</span>
                        <span class="spec-value">Retina display</span>
                    </div>
                </div>
                
                <div class="spec-card">
                    <h3>Health & Fitness</h3>
                    <div class="spec-item">
                        <span class="spec-label">Heart Rate</span>
                        <span class="spec-value">ECG & Optical</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Blood Oxygen</span>
                        <span class="spec-value">SpO2 sensor</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Temperature</span>
                        <span class="spec-value">Wrist temperature</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Workout Detection</span>
                        <span class="spec-value">Automatic</span>
                    </div>
                </div>
                
                <div class="spec-card">
                    <h3>Durability</h3>
                    <div class="spec-item">
                        <span class="spec-label">Case Material</span>
                        <span class="spec-value">Titanium</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Water Resistance</span>
                        <span class="spec-value">100 meters</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Operating Temp</span>
                        <span class="spec-value">-20° to 55° C</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Drop Resistance</span>
                        <span class="spec-value">MIL-STD 810H</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.buy-button').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-3px)';
                alert('Thank you for your interest! This is a demo page.');
            }, 150);
        });

        // Add hover effect to watch
        const watchBody = document.querySelector('.watch-body');
        watchBody.addEventListener('mouseenter', function() {
            this.style.transform = 'translate(-50%, -50%) scale(1.05)';
        });
        
        watchBody.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(-50%, -50%) scale(1)';
        });
    </script>
</body>
</html>