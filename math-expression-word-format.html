<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematical Expression - Word Format</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 40px;
            background-color: white;
        }
        
        .math-expression {
            font-size: 14pt;
            font-weight: normal;
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        
        .copy-instruction {
            color: #666;
            font-size: 10pt;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .stacked {
            position: relative;
            display: inline-block;
            margin: 0 2px;
        }
        
        .stacked .sup {
            position: absolute;
            top: -0.8em;
            right: -0.5em;
            font-size: 0.7em;
            line-height: 1;
        }
        
        .stacked .sub {
            position: absolute;
            bottom: -0.8em;
            right: -0.5em;
            font-size: 0.7em;
            line-height: 1;
        }
        
        sup {
            font-size: 0.8em;
            vertical-align: super;
        }
        
        sub {
            font-size: 0.8em;
            vertical-align: sub;
        }
    </style>
</head>
<body>
    <h1>Mathematical Expression for Word</h1>
    
    <div class="copy-instruction">
        Select the expression below and copy it (Ctrl+C), then paste it into your Word document:
    </div>
    
    <div class="math-expression">
        &lt;<span class="stacked">25<span class="sup">+1</span><span class="sub">-2</span></span> 25~<span class="stacked">39<span class="sup">+1.5</span><span class="sub">-2.5</span></span> <span class="stacked">40<span class="sup">0-</span><span class="sub">4</span></span> &gt; <span class="stacked">40<span class="sup">+1.5</span><span class="sub">-2.5</span></span>
    </div>
    
    <h2>Alternative Formats:</h2>
    
    <h3>Format 1 - Standard HTML (for Word compatibility):</h3>
    <div class="math-expression">
        &lt;25<sup>+1</sup><sub>-2</sub> 25~39<sup>+1.5</sup><sub>-2.5</sub> 40<sup>0-</sup><sub>4</sub> &gt; 40<sup>+1.5</sup><sub>-2.5</sub>
    </div>
    
    <h3>Format 2 - Unicode characters:</h3>
    <div class="math-expression">
        &lt;25⁺¹₋₂ 25~39⁺¹·⁵₋₂.₅ 40⁰⁻₄ &gt; 40⁺¹·⁵₋₂.₅
    </div>
    
    <h3>Format 3 - Plain text with markers:</h3>
    <div class="math-expression">
        &lt;25^(+1)_(-2) 25~39^(+1.5)_(-2.5) 40^(0-)_(4) &gt; 40^(+1.5)_(-2.5)
    </div>
    
    <h3>Format 4 - Word Equation Format:</h3>
    <div class="math-expression">
        &lt;25_(-2)^(+1) 25~39_(-2.5)^(+1.5) 40_(4)^(0-) &gt; 40_(-2.5)^(+1.5)
    </div>
    
    <h3>Format 5 - LaTeX Format:</h3>
    <div class="math-expression" style="font-family: monospace; background-color: #f0f0f0;">
        &lt;25_{-2}^{+1} \, 25 \sim 39_{-2.5}^{+1.5} \, 40_4^{0-} &gt; 40_{-2.5}^{+1.5}
    </div>
    
    <h3>Format 6 - LaTeX with Math Mode:</h3>
    <div class="math-expression" style="font-family: monospace; background-color: #f0f0f0;">
        $&lt;25_{-2}^{+1} \, 25 \sim 39_{-2.5}^{+1.5} \, 40_4^{0-} &gt; 40_{-2.5}^{+1.5}$
    </div>
    
    <h3>Format 7 - LaTeX Display Mode:</h3>
    <div class="math-expression" style="font-family: monospace; background-color: #f0f0f0;">
        $$&lt;25_{-2}^{+1} \, 25 \sim 39_{-2.5}^{+1.5} \, 40_4^{0-} &gt; 40_{-2.5}^{+1.5}$$
    </div>
    
    <h2>LaTeX Usage Instructions:</h2>
    <ul>
        <li><strong>For Word:</strong> Copy Format 5 (without $ symbols) and paste into Word's equation editor</li>
        <li><strong>For LaTeX documents:</strong> Use Format 6 for inline math or Format 7 for display math</li>
        <li><strong>For online LaTeX editors:</strong> Copy any LaTeX format and paste into MathJax or KaTeX enabled editors</li>
        <li><strong>For Markdown with MathJax:</strong> Use Format 6 or 7</li>
    </ul>
    
    <h2>Instructions:</h2>
    <ol>
        <li>Select any of the expressions above</li>
        <li>Copy it (Ctrl+C)</li>
        <li>Paste it into your Word document (Ctrl+V)</li>
        <li>The formatting should be preserved in Word</li>
    </ol>
    
    <p><strong>Note:</strong> Format 1 uses HTML sup/sub tags which Word will recognize and convert to proper superscripts and subscripts when pasted.</p>
</body>
</html>