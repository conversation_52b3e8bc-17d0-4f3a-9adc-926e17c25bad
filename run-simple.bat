@echo off
echo Word Image Link Extractor
echo ========================
echo.

if "%1"=="" (
    echo Please provide a Word document path as argument.
    echo Usage: run-simple.bat [path-to-docx-file]
    echo Example: run-simple.bat test-document.docx
    echo.
    pause
    exit /b 1
)

if not exist "%1" (
    echo Error: File "%1" does not exist.
    echo Please check the file path and try again.
    echo.
    pause
    exit /b 1
)

echo Step 1: Compiling the project...
call mvn clean compile -q
if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Step 2: Copying dependencies...
call mvn dependency:copy-dependencies -q
if %ERRORLEVEL% NEQ 0 (
    echo Dependency copy failed!
    pause
    exit /b 1
)

echo Step 3: Running the program...
echo ==============================
echo.

java -cp "target/classes;target/dependency/*" com.example.WordImageLinkExtractor "%1"

echo.
echo ==============================
echo Program completed.
pause