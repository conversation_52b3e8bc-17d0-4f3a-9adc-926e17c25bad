# Word Image Link Extractor

This Java program extracts hyperlink information from embedded images in Microsoft Word (.docx) documents using Apache POI.

## Features

- Extracts hyperlinks from images embedded in Word documents
- Supports images in paragraphs, tables, headers, and footers
- Provides detailed information including image ID, filename, and link URL
- Handles various Word document structures

## Requirements

- Java 8 or higher
- Maven 3.6 or higher

## Setup and Usage

### Method 1: Using the Simple Batch Script (Recommended for Windows)

```bash
run-simple.bat your-document.docx
```

This script will:
1. Compile the project
2. Copy all dependencies
3. Run the program with the specified document

### Method 2: Using Maven Exec Plugin

First, make sure the project is compiled:
```bash
mvn clean compile
```

Then run:
```bash
mvn exec:java -Dexec.args="your-document.docx"
```

### Method 3: Manual Compilation and Execution

1. Compile the project and copy dependencies:
```bash
mvn clean compile dependency:copy-dependencies
```

2. Run the program:
```bash
java -cp "target/classes;target/dependency/*" com.example.WordImageLinkExtractor your-document.docx
```

On Linux/Mac, use colon instead of semicolon:
```bash
java -cp "target/classes:target/dependency/*" com.example.WordImageLinkExtractor your-document.docx
```

## Creating Test Documents

To test the program, you need a Word document with images that have hyperlinks:

1. Open Microsoft Word and create a new document
2. Insert an image (Insert → Pictures)
3. Right-click the image and select "Hyperlink"
4. Enter a URL (e.g., https://www.google.com)
5. Save as .docx format

## Example Output

```
Found 2 image link(s):
================================================================================
Image Link #1:
  Image ID: rId5
  Image Name: sample-image.png
  Link URL: https://www.example.com

Image Link #2:
  Image ID: rId7
  Image Name: logo.jpg
  Link URL: https://www.google.com
```

## How It Works

The program uses Apache POI to:

1. Parse the Word document structure
2. Iterate through all paragraphs, tables, headers, and footers
3. Search for hyperlink elements in the XML structure
4. Identify hyperlinks associated with embedded images
5. Extract the relationship IDs and resolve them to actual URLs
6. Retrieve image metadata including filenames

## Supported Document Elements

- Paragraphs with linked images
- Tables containing linked images
- Headers and footers with linked images
- Various hyperlink structures in Word documents

## Dependencies

- Apache POI 5.2.4 (poi, poi-ooxml, poi-scratchpad)
- Apache XMLBeans 5.1.1

## Troubleshooting

### "No image links found"
- Make sure your Word document contains images with actual hyperlinks
- Verify the document is in .docx format (not .doc)
- Check that hyperlinks were properly set on images, not just text

### Compilation Issues
- Ensure Java 8+ is installed
- Verify Maven is properly configured
- Check internet connection for dependency downloads

### Runtime Errors
- Verify the document path is correct
- Ensure the document is not corrupted
- Check file permissions

## Notes

- Only works with .docx files (not .doc)
- Requires images to have actual hyperlinks set in Word
- Some complex document structures may require additional handling
- The program handles various XML structures used by different versions of Word