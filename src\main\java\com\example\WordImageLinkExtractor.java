package com.example;

import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.apache.xmlbeans.XmlObject;
import org.openxmlformats.schemas.drawingml.x2006.main.CTHyperlink;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class WordImageLinkExtractor {
    
    public static class ImageLinkInfo {
        private String imageId;
        private String linkUrl;
        private String linkText;
        private String imageName;
        
        public ImageLinkInfo(String imageId, String linkUrl, String linkText, String imageName) {
            this.imageId = imageId;
            this.linkUrl = linkUrl;
            this.linkText = linkText;
            this.imageName = imageName;
        }
        
        // Getters
        public String getImageId() { return imageId; }
        public String getLinkUrl() { return linkUrl; }
        public String getLinkText() { return linkText; }
        public String getImageName() { return imageName; }
        
        @Override
        public String toString() {
            return String.format("ImageLinkInfo{imageId='%s', linkUrl='%s', linkText='%s', imageName='%s'}", 
                    imageId, linkUrl, linkText, imageName);
        }
    }
    
    public static List<ImageLinkInfo> extractImageLinks(String docxFilePath) throws IOException {
        List<ImageLinkInfo> imageLinkInfos = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(docxFilePath);
             XWPFDocument document = new XWPFDocument(fis)) {
            
            // Extract from paragraphs
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                imageLinkInfos.addAll(extractImageLinksFromParagraph(paragraph, document));
            }
            
            // Extract from tables
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            imageLinkInfos.addAll(extractImageLinksFromParagraph(paragraph, document));
                        }
                    }
                }
            }
            
            // Extract from headers and footers
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    imageLinkInfos.addAll(extractImageLinksFromParagraph(paragraph, document));
                }
            }
            
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    imageLinkInfos.addAll(extractImageLinksFromParagraph(paragraph, document));
                }
            }
        }
        
        return imageLinkInfos;
    }
    
    private static List<ImageLinkInfo> extractImageLinksFromParagraph(XWPFParagraph paragraph, XWPFDocument document) {
        List<ImageLinkInfo> imageLinkInfos = new ArrayList<>();
        
        // Method 1: Check for hyperlinks in the paragraph XML
        CTP ctp = paragraph.getCTP();
        XmlCursor cursor = ctp.newCursor();
        
        // Search for hyperlinks containing images
        cursor.selectPath("declare namespace w='http://schemas.openxmlformats.org/wordprocessingml/2006/main' " +
                "declare namespace wp='http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing' " +
                "declare namespace a='http://schemas.openxmlformats.org/drawingml/2006/main' " +
                ".//w:hyperlink");
        
        while (cursor.toNextSelection()) {
            XmlObject obj = cursor.getObject();
            String xmlString = obj.toString();
            
            // Extract hyperlink ID
            String linkId = extractAttribute(xmlString, "r:id");
            if (linkId != null) {
                try {
                    String url = document.getPackagePart().getRelationship(linkId).getTargetURI().toString();
                    
                    // Check if this hyperlink contains images
                    XmlCursor imageCursor = obj.newCursor();
                    imageCursor.selectPath("declare namespace w='http://schemas.openxmlformats.org/wordprocessingml/2006/main' " +
                            "declare namespace wp='http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing' " +
                            "declare namespace a='http://schemas.openxmlformats.org/drawingml/2006/main' " +
                            ".//w:drawing//a:blip");
                    
                    while (imageCursor.toNextSelection()) {
                        XmlObject imageObj = imageCursor.getObject();
                        String imageXml = imageObj.toString();
                        String imageId = extractAttribute(imageXml, "r:embed");
                        if (imageId != null) {
                            try {
                                XWPFPictureData pictureData = document.getPictureDataByID(imageId);
                                String imageName = pictureData != null ? pictureData.getFileName() : "unknown";
                                imageLinkInfos.add(new ImageLinkInfo(imageId, url, "", imageName));
                            } catch (Exception e) {
                                imageLinkInfos.add(new ImageLinkInfo(imageId, url, "", "unknown"));
                            }
                        }
                    }
                    imageCursor.dispose();
                } catch (Exception e) {
                    System.err.println("Error processing hyperlink: " + e.getMessage());
                }
            }
        }
        
        // Method 2: Search for drawing elements with hyperlinks
        cursor.selectPath("declare namespace w='http://schemas.openxmlformats.org/wordprocessingml/2006/main' " +
                "declare namespace wp='http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing' " +
                "declare namespace a='http://schemas.openxmlformats.org/drawingml/2006/main' " +
                ".//w:drawing//a:hlinkClick");
        
        while (cursor.toNextSelection()) {
            XmlObject obj = cursor.getObject();
            if (obj instanceof CTHyperlink) {
                CTHyperlink hyperlink = (CTHyperlink) obj;
                String linkId = hyperlink.getId();
                if (linkId != null) {
                    try {
                        String url = document.getPackagePart().getRelationship(linkId).getTargetURI().toString();
                        
                        // Find associated image in the same drawing
                        XmlCursor imageCursor = ctp.newCursor();
                        imageCursor.selectPath("declare namespace w='http://schemas.openxmlformats.org/wordprocessingml/2006/main' " +
                                "declare namespace wp='http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing' " +
                                "declare namespace a='http://schemas.openxmlformats.org/drawingml/2006/main' " +
                                ".//w:drawing//a:blip");
                        
                        while (imageCursor.toNextSelection()) {
                            XmlObject imageObj = imageCursor.getObject();
                            String imageXml = imageObj.toString();
                            String imageId = extractAttribute(imageXml, "r:embed");
                            if (imageId != null) {
                                try {
                                    XWPFPictureData pictureData = document.getPictureDataByID(imageId);
                                    String imageName = pictureData != null ? pictureData.getFileName() : "unknown";
                                    imageLinkInfos.add(new ImageLinkInfo(imageId, url, "", imageName));
                                } catch (Exception e) {
                                    imageLinkInfos.add(new ImageLinkInfo(imageId, url, "", "unknown"));
                                }
                            }
                        }
                        imageCursor.dispose();
                    } catch (Exception e) {
                        System.err.println("Error processing drawing hyperlink: " + e.getMessage());
                    }
                }
            }
        }
        
        cursor.dispose();
        return imageLinkInfos;
    }
    
    private static String extractAttribute(String xmlString, String attributeName) {
        String searchPattern = attributeName + "=\"";
        int startIndex = xmlString.indexOf(searchPattern);
        if (startIndex != -1) {
            startIndex += searchPattern.length();
            int endIndex = xmlString.indexOf("\"", startIndex);
            if (endIndex != -1) {
                return xmlString.substring(startIndex, endIndex);
            }
        }
        return null;
    }
    
    public static void main(String[] args) {
        if (args.length != 1) {
            System.out.println("Usage: java WordImageLinkExtractor <path-to-docx-file>");
            System.out.println("Example: java WordImageLinkExtractor sample.docx");
            return;
        }
        
        String docxFilePath = args[0];
        
        try {
            List<ImageLinkInfo> imageLinks = extractImageLinks(docxFilePath);
            
            if (imageLinks.isEmpty()) {
                System.out.println("No image links found in the document.");
                System.out.println("Make sure your Word document contains images with hyperlinks.");
            } else {
                System.out.println("Found " + imageLinks.size() + " image link(s):");
                System.out.println("================================================================================");
                
                for (int i = 0; i < imageLinks.size(); i++) {
                    ImageLinkInfo info = imageLinks.get(i);
                    System.out.println("Image Link #" + (i + 1) + ":");
                    System.out.println("  Image ID: " + info.getImageId());
                    System.out.println("  Image Name: " + info.getImageName());
                    System.out.println("  Link URL: " + info.getLinkUrl());
                    if (!info.getLinkText().isEmpty()) {
                        System.out.println("  Link Text: " + info.getLinkText());
                    }
                    System.out.println();
                }
            }
            
        } catch (IOException e) {
            System.err.println("Error reading the Word document: " + e.getMessage());
            System.err.println("Please check if the file exists and is a valid .docx file.");
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}