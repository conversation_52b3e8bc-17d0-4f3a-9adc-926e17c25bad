@echo off
echo Building the project...
call mvn clean compile

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Usage: run.bat [path-to-docx-file]
echo Example: run.bat sample.docx
echo.

if "%1"=="" (
    echo Please provide a Word document path as argument.
    pause
    exit /b 1
)

echo Running Word Image Link Extractor...
echo.
call mvn compile dependency:copy-dependencies -q
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Running the program...
java -cp "target/classes;target/dependency/*" com.example.WordImageLinkExtractor "%1"

pause